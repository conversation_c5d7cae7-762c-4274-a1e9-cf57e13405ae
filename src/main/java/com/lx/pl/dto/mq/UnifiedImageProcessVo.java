package com.lx.pl.dto.mq;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lx.pl.enums.TaskTypeForMq;
import lombok.*;

import java.util.List;

/**
 * 统一图片处理消息体
 * 支持MJ和Flux两种任务类型
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnifiedImageProcessVo {

    /**
     * 任务类型：MJ 或 FLUX
     */
    private String taskType;

    /**
     * 任务ID (jobId for MJ, taskId for Flux)
     */
    private String taskId;

    /**
     * 用户登录名
     */
    private String loginName;

    /**
     * markId
     */
    private String markId;

    /**
     * 是否鉴黄
     */
    private Boolean nsfwCheck;

    /**
     * 图片处理信息列表
     */
    private List<ImageInfo> imageInfos;

    /**
     * Flux专用字段：种子值
     */
    private Integer seed;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageInfo {
        /**
         * 原始图片URL
         */
        private String originalUrl;

        /**
         * 图片文件名
         */
        private String fileName;

        /**
         * 图片宽度
         */
        private Integer width;

        /**
         * 图片高度
         */
        private Integer height;
    }

    /**
     * 获取任务类型枚举
     */
    public TaskTypeForMq getTaskTypeEnum() {
        return TaskTypeForMq.fromType(this.taskType);
    }

    /**
     * 设置任务类型枚举
     */
    public void setTaskTypeEnum(TaskTypeForMq taskTypeEnum) {
        this.taskType = taskTypeEnum != null ? taskTypeEnum.getType() : null;
    }

    /**
     * 判断是否为MJ任务
     */
    public boolean isMjTask() {
        return TaskTypeForMq.MJ.getType().equals(this.taskType);
    }

    /**
     * 判断是否为Flux任务
     */
    public boolean isFluxTask() {
        return TaskTypeForMq.FLUX.getType().equals(this.taskType);
    }
}
