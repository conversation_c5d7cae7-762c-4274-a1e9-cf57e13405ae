package com.lx.pl.Handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/5
 * @description
 */
public class ModelRuleTypeHandler extends BaseTypeHandler<Map<String, Integer>> {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @SneakyThrows
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    Map<String, Integer> map, JdbcType jdbcType) {
        ps.setString(i, objectMapper.writeValueAsString(map));
    }

    @SneakyThrows
    @Override
    public Map<String, Integer> getNullableResult(ResultSet rs, String columnName) {
        return parseJson(rs.getString(columnName));
    }

    @SneakyThrows
    @Override
    public Map<String, Integer> getNullableResult(ResultSet rs, int columnIndex) {
        return parseJson(rs.getString(columnIndex));
    }

    @SneakyThrows
    @Override
    public Map<String, Integer> getNullableResult(CallableStatement cs, int columnIndex) {
        return parseJson(cs.getString(columnIndex));
    }

    @SneakyThrows
    private Map<String, Integer> parseJson(String json) {
        if (json == null || json.trim().isEmpty()) return null;
        return objectMapper.readValue(json, new TypeReference<>() {
        });
    }
}
