package com.lx.pl.pay.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lx.pl.pay.common.domain.PromotionConfigItem;
import com.lx.pl.pay.common.dto.PromotionConfigItemDto;

import java.util.List;

/**
 * <p>
 * 优惠配置项表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface PromotionConfigItemService extends IService<PromotionConfigItem> {

    /**
     * 根据配置ID获取配置项列表
     *
     * @param configId 配置ID
     * @return 配置项列表
     */
    List<PromotionConfigItemDto> getByConfigId(Long configId);

    /**
     * 根据产品类型和计划等级获取配置项
     *
     * @param productType 产品类型
     * @param planLevel 计划等级
     * @param priceInterval 价格间隔
     * @return 配置项列表
     */
    List<PromotionConfigItem> getByProductTypeAndPlanLevel(String productType, String planLevel, String priceInterval);

    /**
     * 根据配置ID和产品类型获取配置项
     *
     * @param configId 配置ID
     * @param productType 产品类型
     * @return 配置项列表
     */
    List<PromotionConfigItem> getByConfigIdAndProductType(Long configId, String productType);

    /**
     * 批量保存配置项
     *
     * @param configItems 配置项列表
     * @return 保存结果
     */
    boolean saveBatchConfigItems(List<PromotionConfigItem> configItems);

    /**
     * 根据配置ID删除所有相关配置项
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    boolean deleteByConfigId(Long configId);

    /**
     * 创建或更新配置项
     *
     * @param configItem 配置项
     * @return 保存结果
     */
    boolean saveOrUpdateConfigItem(PromotionConfigItem configItem);

    /**
     * 获取指定配置的最大折扣配置项
     *
     * @param configId 配置ID
     * @param productType 产品类型
     * @param planLevel 计划等级
     * @param priceInterval 价格间隔
     * @return 最大折扣配置项
     */
    PromotionConfigItem getMaxDiscountItem(Long configId, String productType, String planLevel, String priceInterval);

    List<PromotionConfigItemDto> queryByConfigIdWithCache(Long configId);
}
