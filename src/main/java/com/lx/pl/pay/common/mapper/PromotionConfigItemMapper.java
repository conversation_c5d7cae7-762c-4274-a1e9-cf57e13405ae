package com.lx.pl.pay.common.mapper;

import com.lx.pl.pay.common.domain.PromotionConfigItem;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lx.pl.pay.common.dto.PromotionConfigItemDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 优惠配置项表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
public interface PromotionConfigItemMapper extends BaseMapper<PromotionConfigItem> {

    /**
     * 根据配置ID获取配置项列表
     *
     * @param configId 配置ID
     * @return 配置项列表
     */
    List<PromotionConfigItemDto> getByConfigId(@Param("configId") Long configId);

    /**
     * 根据产品类型和计划等级获取配置项
     *
     * @param productType 产品类型
     * @param planLevel 计划等级
     * @param priceInterval 价格间隔
     * @return 配置项列表
     */
    List<PromotionConfigItem> getByProductTypeAndPlanLevel(
            @Param("productType") String productType,
            @Param("planLevel") String planLevel,
            @Param("priceInterval") String priceInterval
    );

    /**
     * 根据配置ID和产品类型获取配置项
     *
     * @param configId 配置ID
     * @param productType 产品类型
     * @return 配置项列表
     */
    List<PromotionConfigItem> getByConfigIdAndProductType(
            @Param("configId") Long configId,
            @Param("productType") String productType
    );
}
