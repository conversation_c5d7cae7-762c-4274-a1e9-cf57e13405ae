package com.lx.pl.pay.common.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 优惠配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-18
 */
@Data
@Schema(description = "优惠配置表")
public class PromotionConfigVo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private Long id;
    /**
     * 优惠类型：first_buy_sub_old_vip_back,lumen_off
     * @see com.lx.pl.pay.common.enums.PromotionType
     */
    @Schema(description = "优惠类型：first_buy_sub_old_vip_back,lumen_off")
    private String type;

    /**
     * 折扣百分比(0-100)
     */
    @Schema(description = "折扣百分比(0-100)")
    private int off;

//    private VipStandards vipStandards;

    private List<PromotionConfigItemDto> promotionConfigItems;

    private PromotionConfigItemDto usedPromotionConfigItem;

    public PromotionConfigVo() {
    }

    public PromotionConfigVo(Long id, Integer off) {
        if (off == null) {
            off = 0;
        }
        this.id = id;
        this.off = off;
    }
}
