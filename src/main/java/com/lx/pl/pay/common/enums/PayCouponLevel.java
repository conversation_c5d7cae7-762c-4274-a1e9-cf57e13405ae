package com.lx.pl.pay.common.enums;

public enum PayCouponLevel {
    /**
     * /
     * all: [
     * { label: 'All', value: 'all' }
     * ],
     * plan: [
     * { label: 'All', value: 'all' },
     * { label: 'Standard Month', value: 'standard.month' },
     * { label: 'Standard Year', value: 'standard.year' },
     * { label: 'Standard', value: 'standard' },
     * { label: 'Pro Month', value: 'pro.month' },
     * { label: 'Pro Year', value: 'pro.year' },
     * { label: 'Pro', value: 'pro' }
     * ],
     * one: [
     * { label: 'All', value: 'all' },
     * { label: 'Lumen 100', value: 'lumen.100' },
     * { label: 'Lumen 1000', value: 'lumen.1000' },
     * { label: 'Lumen 10000', value: 'lumen.10000' }
     * ]
     */

    ALL("all", "All"),
    STANDARD_MONTH("standard.month", "Standard Month"),
    STANDARD_YEAR("standard.year", "Standard Year"),
    STANDARD("standard", "Standard"),
    PRO_MONTH("pro.month", "Pro Month"),
    PRO_YEAR("pro.year", "Pro Year"),
    PRO("pro", "Pro"),
    LUMEN("lumen", "Lumen");

    private final String code;
    private final String description;

    PayCouponLevel(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static PayCouponLevel getByCode(String code) {
        for (PayCouponLevel level : values()) {
            if (level.getCode().equals(code)) {
                return level;
            }
        }
        return null;
    }

    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }

}
