package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lx.pl.db.mysql.gen.entity.User;
import com.lx.pl.enums.LogicErrorCode;
import com.lx.pl.exception.LogicException;
import com.lx.pl.pay.common.domain.PayCoupon;
import com.lx.pl.pay.common.domain.StripeCoupon;
import com.lx.pl.pay.common.dto.CouponVo;
import com.lx.pl.pay.common.enums.PayCouponLevel;
import com.lx.pl.pay.common.enums.PayCouponProductType;
import com.lx.pl.pay.common.mapper.PayCouponMapper;
import com.lx.pl.pay.common.service.PayCouponLogService;
import com.lx.pl.pay.common.service.PayCouponService;
import com.lx.pl.pay.common.service.PaypalCouponService;
import com.lx.pl.pay.common.service.StripeCouponService;
import com.lx.pl.pay.paypal.model.vo.ProductItem;
import com.lx.pl.pay.stripe.dto.PaymentType;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PayCouponServiceImpl extends ServiceImpl<PayCouponMapper, PayCoupon> implements PayCouponService {

    @Autowired
    private PayCouponLogService payCouponLogService;
    @Autowired
    private StripeCouponService stripeCouponService;
    @Autowired
    private PaypalCouponService paypalCouponService;

    @Override
    public PayCoupon queryValidByCode(String code) {
        if (code == null) {
            return null;
        }
        Long now = System.currentTimeMillis() / 1000;
        PayCoupon one = this.lambdaQuery().eq(PayCoupon::getCode, code)
                .eq(PayCoupon::getValid, true)
                .and(e -> e.le(PayCoupon::getStartTime, now).or().isNull(PayCoupon::getStartTime))
                .and(e -> e.ge(PayCoupon::getRedeemBy, now).or().isNull(PayCoupon::getRedeemBy))
                .one();
        if (one == null) {
            return null;
        }
        if (one.getMaxRedemptions() == -1) return one;
        long count = payCouponLogService.countByCouponId(one.getCode());
        if (one.getMaxRedemptions() > 0 && count >= one.getMaxRedemptions()) {
            return null;
        }
        return one;
    }

    /**
     * 根据优惠券码查询Stripe优惠券码
     *
     * @param couponCode
     * @param stripeItems
     * @return
     */
    @Override
    public CouponVo queryStripeCouponCodeByCouponCodeCheck(String couponCode, List<ProductItem> stripeItems) {
        PayCoupon payCoupon = getPayCoupon(couponCode, stripeItems);


        StripeCoupon stripeCoupon = stripeCouponService.queryBySaleNum(payCoupon.getPercentOff());
        if (stripeCoupon == null) {
            throw new LogicException(LogicErrorCode.COUPON_NOT_FOUND);
        }


        CouponVo couponVo = new CouponVo();
        couponVo.setCode(stripeCoupon.getStripeCouponId());
        couponVo.setPercentOff(stripeCoupon.getPercentOff());
        couponVo.setName(stripeCoupon.getName());
        couponVo.setPayCouponCode(payCoupon.getCode());
        return couponVo;
    }

    @NotNull
    private PayCoupon getPayCoupon(String couponCode, List<ProductItem> items) {
        PayCoupon payCoupon = this.lambdaQuery().eq(PayCoupon::getCode, couponCode)
                .eq(PayCoupon::getValid, true)
                .one();
        if (payCoupon == null) {
            throw new LogicException(LogicErrorCode.COUPON_NOT_FOUND);
        }
        if (payCoupon.getPercentOff() == null) {
            throw new LogicException(LogicErrorCode.COUPON_NOT_FOUND);
        }
        // 比较时间
        Long now = System.currentTimeMillis() / 1000;
        if (payCoupon.getStartTime() != null && now < payCoupon.getStartTime()) {
            throw new LogicException(LogicErrorCode.COUPON_NOT_START);
        }
        if (!payCoupon.getValid() || (payCoupon.getRedeemBy() != null && now > payCoupon.getRedeemBy())) {
            throw new LogicException(LogicErrorCode.COUPON_EXPIRED);
        }
        String level = payCoupon.getLevel();
        if (!PayCouponProductType.ALL.getCode().equals(payCoupon.getProductType())) {
            for (ProductItem stripeItem : items) {
                if (PayCouponProductType.PLAN.getCode().equals(stripeItem.getType().getType())) {
                    String productLevel = stripeItem.getProduct() + "." + stripeItem.getPrice();
                    if (!level.equals(productLevel) && !level.equals(stripeItem.getProduct()) && !PayCouponLevel.ALL.getCode().equals(level)) {
                        throw new LogicException(LogicErrorCode.COUPON_NOT_SUPPORT);
                    }
                } else {
                    String productLevel = "lumen." + stripeItem.getLumen().toString();
                    if (!PayCouponLevel.ALL.getCode().equals(level) && !level.equals(productLevel)) {
                        throw new LogicException(LogicErrorCode.COUPON_NOT_SUPPORT);
                    }
                }
            }
        }
        return payCoupon;
    }

    @Override
    public CouponVo queryCouponCodeByCouponCodeCheck(String couponCode, List<com.lx.pl.pay.paypal.model.vo.ProductItem> items) {
        PayCoupon payCoupon = getPayCoupon(couponCode, items);
        CouponVo couponVo = new CouponVo();
        couponVo.setPercentOff(payCoupon.getPercentOff());
        couponVo.setPayCouponCode(payCoupon.getCode());
        return couponVo;
    }

    @Override
    public CouponVo queryStripeCouponCodeByOff(Integer off) {
        if (off == 0) {
            return null;
        }
        StripeCoupon stripeCoupon = stripeCouponService.queryBySaleNum(off);
        if (stripeCoupon == null) {
            return null;
        }
        CouponVo couponVo = new CouponVo();
        couponVo.setCode(stripeCoupon.getStripeCouponId());
        couponVo.setPercentOff(stripeCoupon.getPercentOff());
        couponVo.setName(stripeCoupon.getName());
        couponVo.setPayCouponCode(null);
        return couponVo;
    }

    @Override
    public boolean checkCouponCodeCanUse(String couponCode, User user, PaymentType paymentType) {
        if (couponCode == null) {
            return true;
        }
        PayCoupon payCoupon = queryValidByCode(couponCode);
        if (payCoupon == null) {
            return false;
        }
        if (payCoupon.getMaxRedemptions() == -1) return true;
        long count = payCouponLogService.countByCouponIdAndUserId(payCoupon.getCode(), user.getId(), paymentType);
        if (count == 0) {
            return true;
        }
        return false;
    }

}