package com.lx.pl.pay.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lx.pl.pay.common.domain.PromotionConfigItem;
import com.lx.pl.pay.common.dto.PromotionConfigItemDto;
import com.lx.pl.pay.common.mapper.PromotionConfigItemMapper;
import com.lx.pl.pay.common.service.PromotionConfigItemService;
import com.lx.pl.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

/**
 * <p>
 * 优惠配置项表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Slf4j
@Service
public class PromotionConfigItemServiceImpl extends ServiceImpl<PromotionConfigItemMapper, PromotionConfigItem> implements PromotionConfigItemService {

    @Autowired
    private RedisService<String> redisService;

    @Override
    public List<PromotionConfigItemDto> getByConfigId(Long configId) {
        return baseMapper.getByConfigId(configId);
    }

    @Override
    public List<PromotionConfigItem> getByProductTypeAndPlanLevel(String productType, String planLevel, String priceInterval) {
        return baseMapper.getByProductTypeAndPlanLevel(productType, planLevel, priceInterval);
    }

    @Override
    public List<PromotionConfigItem> getByConfigIdAndProductType(Long configId, String productType) {
        return baseMapper.getByConfigIdAndProductType(configId, productType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatchConfigItems(List<PromotionConfigItem> configItems) {
        try {
            return this.saveBatch(configItems);
        } catch (Exception e) {
            log.error("批量保存配置项失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByConfigId(Long configId) {
        try {
            LambdaQueryWrapper<PromotionConfigItem> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PromotionConfigItem::getConfigId, configId);
            return this.remove(queryWrapper);
        } catch (Exception e) {
            log.error("根据配置ID删除配置项失败, configId: {}", configId, e);
            return false;
        }
    }

    @Override
    public boolean saveOrUpdateConfigItem(PromotionConfigItem configItem) {
        try {
            return this.saveOrUpdate(configItem);
        } catch (Exception e) {
            log.error("保存或更新配置项失败", e);
            return false;
        }
    }

    @Override
    public PromotionConfigItem getMaxDiscountItem(Long configId, String productType, String planLevel, String priceInterval) {
        try {
            LambdaQueryWrapper<PromotionConfigItem> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(PromotionConfigItem::getConfigId, configId);

            if (productType != null) {
                queryWrapper.eq(PromotionConfigItem::getProductType, productType);
            }
            if (planLevel != null) {
                queryWrapper.eq(PromotionConfigItem::getPlanLevel, planLevel);
            }
            if (priceInterval != null) {
                queryWrapper.eq(PromotionConfigItem::getPriceInterval, priceInterval);
            }

            List<PromotionConfigItem> items = this.list(queryWrapper);

            if (items.isEmpty()) {
                return null;
            }

            // 返回折扣最大的配置项（off值最大）
            return items.stream()
                    .filter(item -> item.getOff() != null)
                    .max(Comparator.comparing(PromotionConfigItem::getOff))
                    .orElse(null);
        } catch (Exception e) {
            log.error("获取最大折扣配置项失败, configId: {}, productType: {}, planLevel: {}, priceInterval: {}",
                    configId, productType, planLevel, priceInterval, e);
            return null;
        }
    }

    @Override
    public List<PromotionConfigItemDto> queryByConfigIdWithCache(Long configId) {
        String key = "promotion:config:item:" + configId;
        String s = redisService.get(key);
        if (s != null) {
            try {
                return writeToList(s);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        } else {
            List<PromotionConfigItemDto> list = baseMapper.getByConfigId(configId);
            try {
                redisService.set(key, writeToString(list), 600);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            return list;
        }
    }

    @Resource(name = "logicObjectMapper")
    private ObjectMapper objectMapper;

    /**
     * 将json数据转换成list
     */
    private List<PromotionConfigItemDto> writeToList(String json) throws JsonProcessingException {
        JavaType javaType = objectMapper.getTypeFactory().constructParametricType(List.class, PromotionConfigItemDto.class);
        return objectMapper.readValue(json, javaType);
    }

    public String writeToString(List<PromotionConfigItemDto> value) throws JsonProcessingException {
        return objectMapper.writeValueAsString(value);
    }

}
