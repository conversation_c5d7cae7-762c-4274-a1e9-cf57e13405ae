package com.lx.pl.db.mysql.gen.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.Instant;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * @description 用户行为记录
 */
@Data
@Document(collection = "user_action_record")
public class UserActionRecord {

    /**
     * 主键id
     */
    @Id
    private String id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 登录账号
     */
    private String userLoginName;

    /**
     * 行为来源平台
     */
    private String platform;

    /**
     * 请求路径
     */
    private String requestUrl;

    /**
     * 请求内容
     */
    private String requestContent;

    /**
     * 功能类型
     */
    private String featureName;

    /**
     * 模型id
     */
    private String modelId;

    /**
     * 请求ip
     */
    private String requestIp;

    /**
     * ip所属国家
     */
    private String ipCountry;

    /**
     * 创建时间
     */
    private Instant createTime;

    /**
     * 更新时间
     */
    private Instant updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
