package com.lx.pl.service;

import com.lx.pl.db.mysql.gen.entity.UserActionRecord;
import com.lx.pl.dto.GenGenericPara;
import com.lx.pl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/1
 * @description
 */
@Slf4j
@Service
public class UserActionRecordService {
    @Resource
    private UserService userService;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private LoadBalanceService loadBalanceService;
    private static final List<String> IGNORE_PATH = List.of("batch-process-task", "select-message-nums", "call-back", "rmbg-status");

    @Async("userActionRecordExecutor")
    public void record(Long userId, String requestUrl, String requestContent, String requestIp, String ipCountry, String platform, String body) {
        try {
            if (IGNORE_PATH.stream().anyMatch(requestUrl::contains)) {
                return;
            }

            UserActionRecord record = new UserActionRecord();
            record.setUserId(userId);
            record.setUserLoginName(userService.getUserById(userId).getLoginName());
            record.setRequestUrl(requestUrl);
            record.setRequestContent(requestContent);
            record.setRequestIp(requestIp);
            record.setIpCountry(ipCountry);
            record.setPlatform(platform);
            String modelId = null;
            String featureName = null;
            if (StringUtils.isNotBlank(body)) {
                GenGenericPara genericPara = JsonUtils.fromString(body, GenGenericPara.class);
                modelId = genericPara.getModel_id();
                featureName = loadBalanceService.judgeFeatures(genericPara);
            }
            record.setModelId(modelId);
            record.setFeatureName(featureName);
            record.setCreateTime(Instant.now());
            record.setUpdateTime(Instant.now());
            record.setCreateBy("System");
            record.setUpdateBy("System");
            mongoTemplate.insert(record);
        } catch (Exception e) {
            log.error("record user action error, userId: {}, ", userId, e);
        }
    }
}
