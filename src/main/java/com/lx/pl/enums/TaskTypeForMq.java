package com.lx.pl.enums;

/**
 * 任务类型枚举，用于MQ消息区分
 *
 * <AUTHOR>
 */
public enum TaskTypeForMq {

    /**
     * Midjourney任务
     */
    MJ("MJ"),

    /**
     * Flux任务
     */
    FLUX("FLUX");

    private final String type;

    TaskTypeForMq(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    /**
     * 根据类型字符串获取枚举值
     */
    public static TaskTypeForMq fromType(String type) {
        if (type == null) {
            return null;
        }

        for (TaskTypeForMq taskType : values()) {
            if (taskType.getType().equalsIgnoreCase(type)) {
                return taskType;
            }
        }

        return null;
    }

}
