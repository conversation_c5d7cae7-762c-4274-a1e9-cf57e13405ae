package com.lx.pl.controller;

import com.lx.pl.config.GoogleOAuthLoginProperties;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * Google OAuth测试控制器
 * 用于调试和验证Google OAuth配置
 */
@Tag(name = "Google OAuth测试接口")
@Slf4j
@RestController
@RequestMapping("/api/test/google-oauth")
public class GoogleOAuthTestController {

    @Autowired
    private GoogleOAuthLoginProperties googleOAuthLoginProperties;

    @Operation(summary = "获取当前Google OAuth配置信息")
    @GetMapping("/config")
    public Map<String, Object> getGoogleOAuthConfig(HttpServletRequest request) {
        String platform = request.getHeader("platform");
        
        Map<String, Object> config = new HashMap<>();
        config.put("platform", platform);
        config.put("webClientId", googleOAuthLoginProperties.getWebClientId());
        config.put("webClientSecret", maskSecret(googleOAuthLoginProperties.getWebClientSecret()));
        config.put("iosClientId", googleOAuthLoginProperties.getIosClientId());
        
        log.info("获取Google OAuth配置 - 平台: {}, Web Client ID: {}", platform, googleOAuthLoginProperties.getWebClientId());
        
        return config;
    }

    @Operation(summary = "验证授权码格式")
    @PostMapping("/validate-code")
    public Map<String, Object> validateAuthCode(@RequestBody Map<String, Object> request, HttpServletRequest httpRequest) {
        String platform = httpRequest.getHeader("platform");
        String code = (String) request.get("code");
        String redirectUri = (String) request.get("redirectUri");
        
        Map<String, Object> result = new HashMap<>();
        result.put("platform", platform);
        result.put("codeLength", code != null ? code.length() : 0);
        result.put("codePrefix", code != null && code.length() > 6 ? code.substring(0, 6) + "..." : code);
        result.put("redirectUri", redirectUri);
        result.put("webClientId", googleOAuthLoginProperties.getWebClientId());
        
        // 验证授权码格式
        boolean isValidFormat = code != null && code.startsWith("4/") && code.length() > 10;
        result.put("isValidCodeFormat", isValidFormat);
        
        log.info("验证授权码 - 平台: {}, 代码长度: {}, 格式有效: {}", platform, code != null ? code.length() : 0, isValidFormat);
        
        return result;
    }

    /**
     * 掩码处理敏感信息
     */
    private String maskSecret(String secret) {
        if (secret == null || secret.length() < 8) {
            return "****";
        }
        return secret.substring(0, 4) + "****" + secret.substring(secret.length() - 4);
    }
}
